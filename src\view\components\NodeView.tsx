/**
 * NodeView - Component for rendering a node
 *
 * This component is responsible for rendering a node and handling node-specific interactions.
 * It combines the interaction handling of NodeView with the content rendering of SimpleNodeView.
 */

import React, { useState, useCallback, useRef, useContext, useEffect } from 'react';
import { NodeModel } from '../../model/NodeModel';
import { nodeController } from '../../controller/NodeController';
import NodeContent from '../../components/Graph/NodeContent';
import { HighlightedNodeContext } from './ViewInteractionLayer';
import NodeTags from '../../components/Tags/NodeTags';
import HoverTagSelector from '../../components/Tags/HoverTagSelector';
import { tagController } from '../../controller/TagController';
import { nodeHighlightService, NodeHighlightEvents } from '../../services/NodeHighlightService';
import './NodeView.css';

export interface NodeViewProps {
  node: NodeModel;
  selected?: boolean;
  onSelect?: (nodeId: string) => void;
  onDoubleClick?: (nodeId: string) => void;
  onNodeClick?: (nodeId: string) => void;
  onAddChild?: (parentId: string, type?: 'tasks' | 'richtext' | 'deadline' | 'table' | 'reminder' | 'image' | 'title', openImmediately?: boolean) => void;
}

export const NodeView: React.FC<NodeViewProps> = ({
  node,
  selected = false,
  onSelect,
  onDoubleClick,
  onNodeClick,
  onAddChild
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showTagSelector, setShowTagSelector] = useState(false);
  const [showNodeTypeMenu, setShowNodeTypeMenu] = useState(false);
  const [contentHeight, setContentHeight] = useState(0);
  const [contentSize, setContentSize] = useState(12); // Font size for content
  const [isHighlighted, setIsHighlighted] = useState(false);
  const [hasSelectedTags, setHasSelectedTags] = useState(false);
  const [highlightColor, setHighlightColor] = useState<string | null>(null);
  const nodeRef = useRef<HTMLDivElement>(null);
  const isRootNode = !node.relationships.parentId;

  // Get the highlighted node context
  const { registerHighlightedNode } = useContext(HighlightedNodeContext);

  // Check if this node is being dragged by the ViewInteractionLayer
  const isDragging = nodeController.isDraggingNode(node.id);

  // Get the current scale from window object (set by GraphBridge)
  const scale = (window as any).mvcTransform?.scale || 1;

  // Check if this is a title node
  const isTitleNode = node.type === 'title';

  // Handle mouse enter
  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
    // Register this node as the highlighted node
    console.log(`[EventFlow] NodeView.handleMouseEnter: node=${node.id}, title="${node.title}", registering as highlighted node`);
    registerHighlightedNode(node.id);

    // Show tag selector after a short delay to prevent accidental activation
    const timer = setTimeout(() => {
      console.log(`[TagSystem] Setting showTagSelector to true for node ${node.id}`);
      setShowTagSelector(true);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [node.id, node.title, registerHighlightedNode]);

  // Handle mouse leave
  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    console.log(`[TagSystem] Setting showTagSelector to false for node ${node.id}`);
    setShowTagSelector(false);
    // Clear this node as the highlighted node
    console.log(`[EventFlow] NodeView.handleMouseLeave: node=${node.id}, title="${node.title}", clearing highlighted node`);
    registerHighlightedNode(null);
  }, [node.id, node.title, registerHighlightedNode]);

  // Clear highlighted node when component unmounts
  useEffect(() => {
    return () => {
      // Only clear if this node is the highlighted one
      console.log(`[NodeView] Component unmounting for node ${node.id}, clearing highlighted node`);
      registerHighlightedNode(null);
    };
  }, [node.id, registerHighlightedNode]);

  // Listen for highlight changes from NodeHighlightService
  useEffect(() => {
    const handleHighlightChange = () => {
      const highlighted = nodeHighlightService.isNodeHighlighted(node.id);
      const tagsSelected = nodeHighlightService.hasSelectedTags();
      const color = nodeHighlightService.getNodeHighlightColor(node.id);
      console.log(`[NodeView] Node ${node.id} highlight change: highlighted=${highlighted}, tagsSelected=${tagsSelected}, color=${color}, nodeTags=${JSON.stringify(node.tags)}`);
      setIsHighlighted(highlighted);
      setHasSelectedTags(tagsSelected);
      setHighlightColor(color);
    };

    // Initial check
    handleHighlightChange();

    // Subscribe to highlight changes
    nodeHighlightService.on(NodeHighlightEvents.HIGHLIGHT_CHANGED, handleHighlightChange);

    return () => {
      nodeHighlightService.off(NodeHighlightEvents.HIGHLIGHT_CHANGED, handleHighlightChange);
    };
  }, [node.id]);

  // Hide tag selector when dragging starts
  useEffect(() => {
    if (isDragging && showTagSelector) {
      console.log(`[TagSystem] Node ${node.id} is being dragged, hiding tag selector`);
      setShowTagSelector(false);
    }
  }, [isDragging, node.id, showTagSelector]);

  // REMOVED: handleMouseDown has been moved to ViewInteractionLayer
  // Node selection is now handled centrally through the hover-based hit detection system

  // Handle double click
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    console.log(`[EventFlow] NodeView.handleDoubleClick: node=${node.id}, title="${node.title}", STOPPING PROPAGATION`);
    e.stopPropagation();

    if (onDoubleClick) {
      console.log(`[EventFlow] NodeView.handleDoubleClick: node=${node.id}, calling onDoubleClick`);
      onDoubleClick(node.id);
    } else if (onNodeClick) {
      // Fallback to onNodeClick if onDoubleClick is not provided
      console.log(`[EventFlow] NodeView.handleDoubleClick: node=${node.id}, calling onNodeClick (fallback)`);
      onNodeClick(node.id);
    }
  }, [node.id, node.title, onDoubleClick, onNodeClick]);

  // Node type items for the selection menu
  const nodeTypeItems = [
    { label: 'Title Node', icon: '🏷️', type: 'title' },
    { label: 'Rich Text', icon: '📝', type: 'richtext' },
    { label: 'Checklist', icon: '✓', type: 'tasks' },
    { label: 'Deadline', icon: '⏰', type: 'deadline' },
    { label: 'Image', icon: '🖼️', type: 'image' },
    { label: 'Reminder', icon: '🔔', type: 'reminder' }
  ];

  // Handle add child button click
  const handleAddChild = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (onAddChild) {
      onAddChild(node.id);
    } else {
      nodeController.addChildNode(node.id);
    }
  }, [node.id, onAddChild]);

  // Handle add button mouse enter
  const handleAddButtonMouseEnter = useCallback(() => {
    console.log('Add button mouse enter - showing node type menu');
    setShowNodeTypeMenu(true);
  }, []);

  // Handle add button mouse leave
  const handleAddButtonMouseLeave = useCallback(() => {
    console.log('Add button mouse leave');
    // Don't hide immediately - let the menu handle its own mouse events
  }, []);

  // Handle menu mouse enter
  const handleMenuMouseEnter = useCallback(() => {
    console.log('Menu mouse enter - keeping menu visible');
    setShowNodeTypeMenu(true);
  }, []);

  // Handle menu mouse leave
  const handleMenuMouseLeave = useCallback(() => {
    console.log('Menu mouse leave - hiding menu');
    setShowNodeTypeMenu(false);
  }, []);

  // Handle node type selection
  const handleNodeTypeSelect = useCallback((type: string, e: React.MouseEvent) => {
    e.stopPropagation();
    console.log(`Node type selected from list: ${type}`);

    if (onAddChild) {
      onAddChild(node.id, type as 'tasks' | 'richtext' | 'deadline' | 'table' | 'reminder' | 'image' | 'title', true);
    } else {
      nodeController.addChildNode(node.id, {
        type: type as 'tasks' | 'richtext' | 'deadline' | 'table' | 'reminder' | 'image' | 'title',
        title: type === 'title' ? 'Title Node' : 'New Node'
      });
    }
    setShowNodeTypeMenu(false);
  }, [node.id, onAddChild]);

  // Handle delete button click
  const handleDelete = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation();

    // Use the nodeController's deleteNode method which now shows a custom modal
    await nodeController.deleteNode(node.id);
  }, [node.id]);

  // Handle toggle completed
  const handleToggleCompleted = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    nodeController.toggleNodeCompleted(node.id);
  }, [node.id]);





  // Handle tag selection
  const handleTagsChange = useCallback((nodeId: string, tagIds: string[]) => {
    console.log(`Updating tags for node ${nodeId}:`, tagIds);

    // Update the node model with the new tags
    nodeController.updateNode(nodeId, { tags: tagIds });
  }, []);

  // Handle tag selector mouse events
  const handleSelectorMouseEnter = useCallback(() => {
    // Keep the selector visible when mouse is over it
    setShowTagSelector(true);
  }, []);

  const handleSelectorMouseLeave = useCallback(() => {
    // Hide the selector when mouse leaves it
    setShowTagSelector(false);
  }, []);

  // Handle color button click to show color picker
  const handleColorChange = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();

    // Get the color button element that was clicked
    const colorButton = e.currentTarget as HTMLElement;

    if (colorButton) {
      // Get the button's position
      const buttonRect = colorButton.getBoundingClientRect();

      // Position the color picker directly over the color button
      // This ensures it's visually connected to the button that opened it
      const pickerX = buttonRect.left + buttonRect.width / 2;
      const pickerY = buttonRect.top + buttonRect.height / 2; // Center of the button

      console.log(`Positioning color picker over color button: (${pickerX}, ${pickerY})`);

      // Use the NodeController's showColorPicker method
      nodeController.showColorPicker(node.id, {
        x: pickerX,
        y: pickerY
      });
    } else {
      // Fallback to mouse position if button element not found
      console.warn(`Color button element not found, using mouse position instead`);
      nodeController.showColorPicker(node.id, {
        x: e.clientX,
        y: e.clientY
      });
    }
  }, [node.id]);

  // Handle content height change with validation
  const handleContentHeightChange = useCallback((height: number) => {
    // Validate height is a valid number
    if (isNaN(height) || !isFinite(height) || height < 0) {
      return;
    }

    // Cap height at a reasonable maximum
    const maxContentHeight = 10000;
    if (height > maxContentHeight) {
      height = maxContentHeight;
    }

    setContentHeight(height);

    // Update node dimensions based on content height
    // Add some padding for the header and controls
    const headerHeight = 40; // Approximate height of the header
    const newHeight = height + headerHeight + 20; // Add padding

    // Ensure we maintain the original width (or a minimum width)
    // Default node width should be at least 336px (standard node width)
    const originalWidth = node.dimensions.width;
    const minWidth = 336;
    const maxWidth = 10000; // Maximum reasonable width

    // Validate width
    let newWidth = Math.max(originalWidth, minWidth);
    if (newWidth > maxWidth) {
      newWidth = maxWidth;
    }

    // Only update if height has actually changed by more than 5px
    // This prevents infinite update loops
    if (Math.abs(node.dimensions.height - newHeight) > 5) {
      nodeController.updateNode(node.id, {
        dimensions: {
          width: newWidth,
          height: newHeight
        }
      });
    }
  }, [node]);

  // Handle segment update
  const handleSegmentUpdate = useCallback((segmentId: number, content: string) => {
    const updatedSegments = node.segments?.map(segment => {
      if (segment.id === segmentId) {
        return { ...segment, content };
      }
      return segment;
    });

    nodeController.updateNode(node.id, { segments: updatedSegments });
  }, [node]);

  // Handle image update
  const handleImageUpdate = useCallback((segmentId: number, imageData: any) => {
    const updatedSegments = node.segments?.map(segment => {
      if (segment.id === segmentId) {
        return { ...segment, imageData };
      }
      return segment;
    });

    nodeController.updateNode(node.id, { segments: updatedSegments });
  }, [node]);

  // Handle dimensions change with validation
  const handleDimensionsChange = useCallback((dimensions: { width: number; height: number }) => {
    // Define reasonable limits
    const minWidth = 336; // Minimum width (standard node width)
    const maxWidth = 10000; // Maximum reasonable width
    const maxHeight = 10000; // Maximum reasonable height
    const originalWidth = node.dimensions.width;

    // Validate width - ensure it's a valid number within reasonable limits
    let newWidth = dimensions.width;
    if (isNaN(newWidth) || !isFinite(newWidth) || newWidth <= 0) {
      newWidth = originalWidth || minWidth;
    } else if (newWidth > maxWidth) {
      newWidth = maxWidth;
    } else {
      // Ensure minimum width and maintain original width if larger
      newWidth = Math.max(newWidth, originalWidth, minWidth);
    }

    // Validate height - ensure it's a valid number within reasonable limits
    let newHeight = dimensions.height;
    if (isNaN(newHeight) || !isFinite(newHeight) || newHeight <= 0) {
      newHeight = node.dimensions.height || 300; // Default height
    } else if (newHeight > maxHeight) {
      newHeight = maxHeight;
    }

    // Only update if dimensions have actually changed by more than 5px
    // This prevents infinite update loops
    if (Math.abs(node.dimensions.height - newHeight) > 5 ||
        Math.abs(node.dimensions.width - newWidth) > 5) {
      nodeController.updateNode(node.id, {
        dimensions: {
          width: newWidth,
          height: newHeight
        }
      });
    }
  }, [node]);

  // Determine node style based on type
  const getNodeStyle = useCallback(() => {
    // Determine highlighting effects
    let highlightBorder = 'none';
    let highlightBoxShadow = isDragging ? '0 4px 20px rgba(0, 0, 0, 0.3)' : '0 2px 10px rgba(0, 0, 0, 0.1)';
    let opacity = 1;
    let transform = 'none';

    if (hasSelectedTags) {
      if (isHighlighted && highlightColor) {
        // Node has selected tags - add glowing border with tag color
        highlightBorder = `3px solid ${highlightColor}`;

        // Create RGB values for the glow effect
        const rgb = highlightColor.startsWith('#')
          ? highlightColor.slice(1).match(/.{2}/g)?.map(x => parseInt(x, 16)) || [66, 153, 225]
          : [66, 153, 225];

        highlightBoxShadow = `0 0 0 6px rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, 0.4), 0 8px 20px rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, 0.6)`;
        transform = 'scale(1.02)'; // Slight scale up for pop effect
      } else {
        // Node doesn't have selected tags - heavily desaturate and shrink slightly
        opacity = 0.25;
        transform = 'scale(0.98)';
      }
    }

    // Override with selection border if selected
    if (selected) {
      highlightBorder = '3px solid #fff';
      highlightBoxShadow = '0 0 0 6px rgba(255, 255, 255, 0.5), 0 8px 20px rgba(255, 255, 255, 0.3)';
    }

    const baseStyle: React.CSSProperties = {
      position: 'absolute',
      left: `${node.position.x}px`,
      top: `${node.position.y}px`,
      width: `${node.dimensions.width}px`,
      height: `${node.dimensions.height}px`,
      backgroundColor: node.color,
      borderRadius: '8px',
      boxShadow: highlightBoxShadow,
      cursor: isRootNode ? 'default' : (isDragging ? 'grabbing' : 'grab'),
      zIndex: isDragging ? 1000 : (selected ? 100 : (isHighlighted ? 50 : 10)), // Higher z-index for highlighted nodes
      overflow: 'visible', // Changed from 'hidden' to 'visible' to show buttons outside node boundaries
      display: 'flex',
      flexDirection: 'column',
      border: highlightBorder,
      opacity: opacity,
      transform: transform,
      pointerEvents: 'auto', // Ensure the node captures pointer events
      userSelect: 'none', // Prevent text selection during dragging
      touchAction: 'none', // Prevent default touch actions
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)', // Smooth transitions with easing
      '--scale-factor': scale // Add scale as a CSS variable for tag scaling
    } as React.CSSProperties;

    // Adjust style based on node type
    switch (node.type) {
      case 'title':
        return {
          ...baseStyle,
          borderRadius: '16px',
          backgroundColor: node.color,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
        };
      case 'tasks':
        return {
          ...baseStyle,
          backgroundColor: node.color,
          borderLeft: '4px solid #2c5282'
        };
      case 'deadline':
        return {
          ...baseStyle,
          backgroundColor: node.color,
          borderTop: '4px solid #c53030'
        };
      default:
        return baseStyle;
    }
  }, [node, isDragging, selected, isRootNode, scale, isHighlighted, hasSelectedTags, highlightColor]);

  return (
    <>
      {/* Node element */}
      <div
        ref={nodeRef}
        className={`node ${node.type || ''} ${isHovered ? 'hovered' : ''} ${isDragging ? 'dragging' : ''} ${selected ? 'selected' : ''} ${node.completed ? 'completed' : ''}`}
        style={getNodeStyle()}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onDoubleClick={handleDoubleClick}
        data-node-id={node.id}
      >
        {/* Node tags - displayed above the node */}
        <NodeTags tagIds={node.tags || []} />

        {/* Tag selector - shown on hover and hidden when dragging */}
        {isHovered && showTagSelector && !isDragging && (
          <>
            {console.log(`[TagSystem] Rendering HoverTagSelector for node ${node.id}`)}
            <HoverTagSelector
              nodeId={node.id}
              selectedTags={node.tags || []}
              onTagsChange={handleTagsChange}
              onMouseEnter={handleSelectorMouseEnter}
              onMouseLeave={handleSelectorMouseLeave}
            />
          </>
        )}
        {/* Node header */}
        <div className="node-header" style={{ padding: '8px', borderBottom: '1px solid rgba(0, 0, 0, 0.1)' }}>
          <div className="node-title" style={{ fontWeight: 'bold', fontSize: '14px' }}>
            {node.title || 'Untitled'}
          </div>
          {node.label && (
            <div className="node-label" style={{ fontSize: '12px', opacity: 0.7 }}>
              {node.label}
            </div>
          )}
        </div>

        {/* Node content - only for non-title nodes */}
        {!isTitleNode && (
          <div className="node-content" style={{ flex: 1, padding: '8px', overflow: 'auto', backgroundColor: 'var(--node-bg)', color: 'var(--node-text)' }}>
            {node.segments && node.segments.length > 0 ? (
              <NodeContent
                segments={node.segments?.map(segment => ({
                  ...segment,
                  type: segment.type as 'tasks' | 'richtext' | 'deadline' | 'table' | 'reminder' | 'image' | 'title'
                })) || []}
                maxHeight={400}
                onHeightChange={handleContentHeightChange}
                onSegmentUpdate={handleSegmentUpdate}
                onImageUpdate={handleImageUpdate}
                scale={scale}
                calculatedFontSize={contentSize}
                onDimensionsChange={handleDimensionsChange}
              />
            ) : (
              <div style={{ padding: '10px', opacity: 0.7 }}>No content</div>
            )}
          </div>
        )}

        {/* Node controls (only shown when hovered) - Positioned like in legacy implementation */}
        {isHovered && (
          <>
            {/* Add button - positioned at bottom center outside the node */}
            <div style={{ position: 'relative' }}>
              <button
                className="add-button node-button"
                onClick={handleAddChild}
                onMouseEnter={handleAddButtonMouseEnter}
                onMouseLeave={handleAddButtonMouseLeave}
                style={{
                  position: 'absolute',
                  bottom: '-0px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '48px',
                  height: '48px',
                  borderRadius: '50%',
                  backgroundColor: '#10b981',
                  color: 'white',
                  border: 'none',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '24px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
                  zIndex: 1000
                }}
              >
                +
              </button>

              {/* Node type selection menu */}
              {showNodeTypeMenu && (
                <div
                  className="node-type-menu"
                  onMouseEnter={handleMenuMouseEnter}
                  onMouseLeave={handleMenuMouseLeave}
                  style={{
                    position: 'absolute',
                    bottom: '100%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.25)',
                    padding: '8px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '8px',
                    minWidth: '120px',
                    zIndex: 1002,
                    paddingBottom: '16px',
                    marginBottom: '-8px',
                    border: '1px solid rgba(0,0,0,0.1)',
                    pointerEvents: 'auto'
                  }}
                >
                  {nodeTypeItems.map((item) => (
                    <button
                      key={item.type}
                      className="node-type-item"
                      onClick={(e) => handleNodeTypeSelect(item.type, e)}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '8px',
                        border: 'none',
                        background: 'white',
                        color: '#1f2937',
                        cursor: 'pointer',
                        borderRadius: '4px',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        width: '100%',
                        textAlign: 'left',
                        whiteSpace: 'nowrap'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f3f4f6';
                        e.currentTarget.style.transform = 'translateX(2px)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'white';
                        e.currentTarget.style.transform = 'translateX(0px)';
                      }}
                    >
                      <span style={{ fontSize: '16px', width: '20px', textAlign: 'center' }}>
                        {item.icon}
                      </span>
                      <span style={{ fontSize: '14px', fontWeight: '500' }}>
                        {item.label}
                      </span>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Delete button - positioned at top right */}
            <button
              className="delete-button node-button"
              onClick={handleDelete}
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                width: '32px',
                height: '32px',
                borderRadius: '6px',
                backgroundColor: '#ef4444',
                color: 'white',
                border: 'none',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '16px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
                zIndex: 1000
              }}
            >
              🗑
            </button>
          </>
        )}

        {/* Bottom left controls container - only shown when hovered or when completed */}
        {(isHovered || node.completed) && (
          <div className="bottom-left-controls" style={{
            position: 'absolute',
            bottom: '10px',
            left: '10px',
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            zIndex: 1000
          }}>
            {/* Completable checkbox - always visible when completed */}
            <div
              className={`completable-checkbox ${node.completed ? 'completed' : ''}`}
              onClick={handleToggleCompleted}
              style={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: node.completed ? 'rgba(34, 197, 94, 0.8)' : 'white',
                border: `2px solid ${node.completed ? 'rgba(34, 197, 94, 0.8)' : '#ccc'}`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
              }}
            >
              {node.completed && (
                <span style={{ color: 'white', fontSize: '22px', fontWeight: 'bold' }}>✓</span>
              )}
            </div>

            {/* Color button - only visible on hover */}
            {isHovered && (
              <button
                className="color-picker-button"
                onClick={handleColorChange}
                style={{ backgroundColor: node.color }}
              >
                <span style={{ fontSize: '16px' }}>🎨</span>
              </button>
            )}
          </div>
        )}
      </div>
    </>
  );
};
